#!/usr/bin/env python3
"""
测试脚本：验证 deploy_and_verify 节点中 device_id 的正确格式化
"""

def test_device_id_formatting():
    """测试 device_id 在 topic 中的格式化"""
    
    # 模拟测试计划数据
    test_plan = {
        "device_log_topic": "/sensor/{DEVICE_ID}/data",
        "sequence": [
            {
                "name": "Check sensor data",
                "expected_log_contains": "\"temperature\":",
                "timeout_seconds": 30
            }
        ]
    }
    
    # 模拟设备ID
    device_id = "esp32_sensor_001"
    
    # 测试修复前的逻辑（错误的）
    print("=== 修复前的逻辑（错误）===")
    topic_to_verify_old = test_plan.get("device_log_topic", f"/debug/{device_id}/log")
    print(f"原始 topic: {test_plan['device_log_topic']}")
    print(f"错误结果: {topic_to_verify_old}")
    print(f"问题: {{DEVICE_ID}} 占位符没有被替换\n")
    
    # 测试修复后的逻辑（正确的）
    print("=== 修复后的逻辑（正确）===")
    raw_topic = test_plan.get("device_log_topic", f"/debug/{device_id}/log")
    topic_to_verify_new = raw_topic.format(DEVICE_ID=device_id)
    print(f"原始 topic: {test_plan['device_log_topic']}")
    print(f"正确结果: {topic_to_verify_new}")
    print(f"修复: {{DEVICE_ID}} 占位符被正确替换为实际的 device_id\n")
    
    # 验证结果
    expected_topic = f"/sensor/{device_id}/data"
    if topic_to_verify_new == expected_topic:
        print("✅ 测试通过：device_id 格式化正确")
        return True
    else:
        print(f"❌ 测试失败：期望 {expected_topic}，实际 {topic_to_verify_new}")
        return False

def test_fallback_topic():
    """测试当没有 device_log_topic 时的回退逻辑"""
    
    print("\n=== 测试回退逻辑 ===")
    
    # 模拟没有 device_log_topic 的测试计划
    test_plan = {
        "sequence": [
            {
                "name": "Check debug log",
                "expected_log_contains": "\"status\":",
                "timeout_seconds": 30
            }
        ]
    }
    
    device_id = "esp32_debug_002"
    
    # 测试回退逻辑
    raw_topic = test_plan.get("device_log_topic", f"/debug/{device_id}/log")
    topic_to_verify = raw_topic.format(DEVICE_ID=device_id)
    
    expected_fallback = f"/debug/{device_id}/log"
    
    print(f"回退 topic: {topic_to_verify}")
    
    if topic_to_verify == expected_fallback:
        print("✅ 回退逻辑测试通过")
        return True
    else:
        print(f"❌ 回退逻辑测试失败：期望 {expected_fallback}，实际 {topic_to_verify}")
        return False

if __name__ == "__main__":
    print("开始测试 device_id 格式化修复...")
    
    test1_passed = test_device_id_formatting()
    test2_passed = test_fallback_topic()
    
    print(f"\n=== 测试总结 ===")
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！device_id 格式化问题已修复")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
